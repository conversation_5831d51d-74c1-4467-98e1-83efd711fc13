import { useEffect } from "react";
import { useAccessStore } from "../store";
import { useAppConfig } from "../store/config";
import { useUserStore } from "../store/user";
import { UserConfig } from "../store/user";

// 这个组件负责监听配置变化并保存到用户配置文件
export function UserConfigSaver() {
  const userStore = useUserStore();
  const accessStore = useAccessStore();
  const appConfig = useAppConfig();

  // 监听配置变化并保存
  useEffect(() => {
    // 检查是否在浏览器环境中
    if (typeof window === 'undefined') {
      return;
    }

    // 如果用户未登录，不执行任何操作
    if (!userStore || !userStore.isLoggedIn || !userStore.isLoggedIn() || !userStore.currentUser) {
      console.log("[UserConfigSaver] User not logged in, skipping config save setup");
      return;
    }

    try {
      // 获取当前用户名，在 effect 内部获取以避免依赖问题
      const username = userStore.currentUser.username;
      if (!username) {
        console.error("[UserConfigSaver] Username is empty");
        return;
      }

      console.log(`[UserConfigSaver] Setting up config save for user: ${username}`);

      // 创建一个防抖函数，避免频繁保存
      let saveTimeout: NodeJS.Timeout | null = null;

      const saveConfig = () => {
        if (saveTimeout) {
          clearTimeout(saveTimeout);
        }

        saveTimeout = setTimeout(async () => {
          try {
            // 检查用户是否仍然登录
            if (!userStore.isLoggedIn() || !userStore.currentUser) {
              console.log("[UserConfigSaver] User no longer logged in, skipping save");
              return;
            }

            // 先获取现有的用户配置，确保保留密码
            const existingConfig = await userStore.getUserConfig(username);

            // 构建用户配置，保留原有密码
            // 使用类型断言来避免类型错误
            const userConfig = {
              ...appConfig,
              // 添加 accessStore 中的所有重要配置
              useCustomConfig: accessStore.useCustomConfig,
              provider: accessStore.provider,
              openaiApiKey: accessStore.openaiApiKey || "",
              openaiUrl: accessStore.openaiUrl || "",
              googleApiKey: accessStore.googleApiKey || "",
              googleUrl: accessStore.googleUrl || "",
              googleApiVersion: accessStore.googleApiVersion || "",
              googleSafetySettings: accessStore.googleSafetySettings,
              anthropicApiKey: accessStore.anthropicApiKey || "",
              anthropicUrl: accessStore.anthropicUrl || "",
              anthropicApiVersion: accessStore.anthropicApiVersion || "",
              deepseekApiKey: accessStore.deepseekApiKey || "",
              deepseekUrl: accessStore.deepseekUrl || "",
              siliconflowApiKey: accessStore.siliconflowApiKey || "",
              siliconflowUrl: accessStore.siliconflowUrl || "",
              azureApiKey: accessStore.azureApiKey || "",
              azureUrl: accessStore.azureUrl || "",
              azureApiVersion: accessStore.azureApiVersion || "",
              baiduApiKey: accessStore.baiduApiKey || "",
              baiduUrl: accessStore.baiduUrl || "",
              baiduSecretKey: accessStore.baiduSecretKey || "",
              bytedanceApiKey: accessStore.bytedanceApiKey || "",
              bytedanceUrl: accessStore.bytedanceUrl || "",
              alibabaApiKey: accessStore.alibabaApiKey || "",
              alibabaUrl: accessStore.alibabaUrl || "",
              moonshotApiKey: accessStore.moonshotApiKey || "",
              moonshotUrl: accessStore.moonshotUrl || "",
              stabilityApiKey: accessStore.stabilityApiKey || "",
              stabilityUrl: accessStore.stabilityUrl || "",
              tencentSecretKey: accessStore.tencentSecretKey || "",
              tencentSecretId: accessStore.tencentSecretId || "",
              tencentUrl: accessStore.tencentUrl || "",
              iflytekApiKey: accessStore.iflytekApiKey || "",
              iflytekApiSecret: accessStore.iflytekApiSecret || "",
              iflytekUrl: accessStore.iflytekUrl || "",
              xaiApiKey: accessStore.xaiApiKey || "",
              xaiUrl: accessStore.xaiUrl || "",
              chatglmApiKey: accessStore.chatglmApiKey || "",
              chatglmUrl: accessStore.chatglmUrl || "",
              accessCode: accessStore.accessCode || "",
              // 保留原有密码
              password: existingConfig?.password || "",
            } as UserConfig;

            // 保存用户配置
            await userStore.saveUserConfig(username, userConfig);
            console.log(`[UserConfig] Saved config for user ${username}`);
          } catch (error) {
            console.error("[UserConfig] Failed to save user config:", error);
          }
        }, 2000); // 2秒防抖
      };

      // 使用 useEffect 监听 accessStore 和 appConfig 的变化
      // 这些值会在组件内部被跟踪，当它们变化时会触发 saveConfig

      // 当 accessStore 或 appConfig 变化时，触发保存
      const accessStoreLastUpdateTime = accessStore.lastUpdateTime;
      const appConfigLastUpdate = appConfig.lastUpdate;

      // eslint-disable-next-line
      useEffect(() => {
        saveConfig();
        // eslint-disable-next-line react-hooks/exhaustive-deps
      }, [accessStoreLastUpdateTime, appConfigLastUpdate]);

      // 组件卸载时清理
      return () => {
        if (saveTimeout) {
          clearTimeout(saveTimeout);
        }
      };
    } catch (error) {
      console.error("[UserConfigSaver] Error setting up config save:", error);
      return () => {}; // 返回空清理函数
    }
  }, [userStore, accessStore, appConfig]);

  // 这是一个纯功能组件，不渲染任何内容
  return null;
}


