// 测试配置保存功能的脚本
// 这个脚本可以在浏览器控制台中运行来测试配置保存

console.log("=== 测试自定义模型配置保存功能 ===");

// 1. 检查当前的 accessStore 状态
console.log("1. 当前 accessStore 状态:");
if (typeof window !== 'undefined' && window.useAccessStore) {
  const accessStore = window.useAccessStore.getState();
  console.log({
    useCustomConfig: accessStore.useCustomConfig,
    provider: accessStore.provider,
    openaiUrl: accessStore.openaiUrl,
    openaiApiKey: accessStore.openaiApiKey ? "***已设置***" : "未设置",
  });
} else {
  console.log("无法访问 accessStore");
}

// 2. 检查当前的 userStore 状态
console.log("\n2. 当前 userStore 状态:");
if (typeof window !== 'undefined' && window.useUserStore) {
  const userStore = window.useUserStore.getState();
  console.log({
    isLoggedIn: userStore.isLoggedIn(),
    currentUser: userStore.currentUser?.username || "未登录",
  });
} else {
  console.log("无法访问 userStore");
}

// 3. 模拟设置自定义配置
console.log("\n3. 模拟设置自定义 OpenAI 配置:");
if (typeof window !== 'undefined' && window.useAccessStore) {
  const accessStore = window.useAccessStore;

  // 设置自定义配置
  accessStore.getState().update((access) => {
    access.useCustomConfig = true;
    access.provider = "OpenAI";
    access.openaiUrl = "https://api.custom-openai.com/v1";
    access.openaiApiKey = "test-api-key-123";
  });

  console.log("已设置自定义配置:");
  const newState = accessStore.getState();
  console.log({
    useCustomConfig: newState.useCustomConfig,
    provider: newState.provider,
    openaiUrl: newState.openaiUrl,
    openaiApiKey: newState.openaiApiKey ? "***已设置***" : "未设置",
  });
} else {
  console.log("无法设置自定义配置");
}

// 4. 检查配置是否会被保存
console.log("\n4. 检查配置保存逻辑:");
console.log("请手动点击设置页面的'保存设置'按钮，然后查看控制台日志");
console.log("应该看到类似以下的日志:");
console.log("  [Settings] Current accessStore state: {...}");
console.log("  [Settings] Saving user config with accessStore data: {...}");

// 5. 提供验证步骤
console.log("\n5. 验证步骤:");
console.log("a) 确保已登录用户");
console.log("b) 在设置页面设置自定义 OpenAI URL");
console.log("c) 点击保存设置");
console.log("d) 查看用户配置文件 (app/users/[username].json)");
console.log("e) 确认文件中包含 useCustomConfig 和 openaiUrl 字段");

// 6. 检查用户配置文件内容的函数
window.checkUserConfig = async function(username) {
  if (!username) {
    console.log("请提供用户名");
    return;
  }

  try {
    const response = await fetch(`/api/user-config?username=${encodeURIComponent(username)}`);
    if (response.ok) {
      const config = await response.json();
      console.log(`用户 ${username} 的配置:`, {
        useCustomConfig: config.useCustomConfig,
        provider: config.provider,
        openaiUrl: config.openaiUrl,
        hasOpenaiApiKey: !!config.openaiApiKey,
        hasPassword: !!config.password,
      });
    } else {
      console.log(`无法获取用户 ${username} 的配置:`, response.status);
    }
  } catch (error) {
    console.error("获取用户配置时出错:", error);
  }
};

console.log("\n使用 checkUserConfig('用户名') 来检查特定用户的配置");
console.log("例如: checkUserConfig('aspower')");

// 7. 测试模型选择同步功能
window.testModelSync = function() {
  console.log("\n=== 测试模型选择同步功能 ===");

  if (typeof window !== 'undefined' && window.useAccessStore && window.useAppConfig) {
    const accessStore = window.useAccessStore;
    const appConfig = window.useAppConfig;

    console.log("1. 当前状态:");
    console.log("accessStore.provider:", accessStore.getState().provider);
    console.log("modelConfig.providerName:", appConfig.getState().modelConfig.providerName);
    console.log("modelConfig.model:", appConfig.getState().modelConfig.model);

    console.log("\n2. 模拟选择OpenAI模型:");
    // 模拟选择OpenAI的gpt-4模型
    appConfig.getState().update((config) => {
      config.modelConfig.model = "gpt-4";
      config.modelConfig.providerName = "OpenAI";
    });

    console.log("更新后的状态:");
    console.log("accessStore.provider:", accessStore.getState().provider);
    console.log("modelConfig.providerName:", appConfig.getState().modelConfig.providerName);
    console.log("modelConfig.model:", appConfig.getState().modelConfig.model);

    console.log("\n3. 检查同步是否正常:");
    const currentAccessProvider = accessStore.getState().provider;
    const currentModelProvider = appConfig.getState().modelConfig.providerName;

    if (currentAccessProvider === currentModelProvider) {
      console.log("✅ 同步正常！两个provider一致");
    } else {
      console.log("❌ 同步异常！provider不一致");
      console.log("需要手动同步...");

      // 手动同步
      accessStore.getState().update((access) => {
        access.provider = currentModelProvider;
      });

      console.log("手动同步后:");
      console.log("accessStore.provider:", accessStore.getState().provider);
    }
  } else {
    console.log("无法访问store");
  }
};

console.log("\n使用 testModelSync() 来测试模型选择同步功能");
