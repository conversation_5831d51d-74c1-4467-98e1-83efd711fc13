# 自定义模型配置保存问题修复

## 问题描述
用户反馈保存设置时，自定义模型的配置（如OpenAI自定义网址）没有被正确保存到JSON配置文件中。

## 问题分析
通过检查用户配置文件（如 `app/users/aspower.json`），发现确实缺少了关键的accessStore配置：
- `useCustomConfig` - 是否使用自定义配置
- `openaiUrl` - 自定义OpenAI网址
- `provider` - 选择的提供商
- 其他提供商的URL配置

## 修复内容

### 1. 修复设置保存逻辑 (`app/components/settings.tsx`)
- 添加了更完整的accessStore配置保存
- 增加了调试日志以便排查问题
- 确保保存时包含所有重要的配置字段

**主要修改：**
```javascript
// 添加了更多配置字段
useCustomConfig: accessStore.useCustomConfig,
provider: accessStore.provider,
openaiUrl: accessStore.openaiUrl,
googleApiVersion: accessStore.googleApiVersion,
googleSafetySettings: accessStore.googleSafetySettings,
anthropicApiVersion: accessStore.anthropicApiVersion,
// ... 其他配置
```

### 2. 修复配置加载逻辑 (`app/components/auth.tsx`)
- 增强了登录时的配置加载逻辑
- 确保所有accessStore配置都能正确恢复
- 添加了调试日志

**主要修改：**
```javascript
// 恢复提供商选择
if (userConfigAny.provider !== undefined) {
  access.provider = userConfigAny.provider;
}

// 恢复更多配置字段
if (userConfigAny.googleApiVersion !== undefined) {
  access.googleApiVersion = userConfigAny.googleApiVersion;
}
// ... 其他配置恢复
```

### 3. 修复用户配置保存器 (`app/components/user-config-saver.tsx`)
- 确保自动保存功能也包含所有必要配置
- 与手动保存保持一致

## 测试方法

### 1. 使用测试脚本
运行 `test-config-save.js` 脚本来验证修复：
```javascript
// 在浏览器控制台中运行
checkUserConfig('用户名');
```

### 2. 手动测试步骤
1. 登录用户账户
2. 进入设置页面
3. 选择OpenAI提供商
4. 设置自定义网址（如：`https://api.custom-openai.com/v1`）
5. 勾选"使用自定义配置"
6. 点击"保存设置"
7. 检查用户配置文件是否包含相关配置

### 3. 验证配置文件
检查 `app/users/[username].json` 文件应包含：
```json
{
  "useCustomConfig": true,
  "provider": "OpenAI",
  "openaiUrl": "https://api.custom-openai.com/v1",
  "openaiApiKey": "...",
  // ... 其他配置
}
```

## 调试日志
修复后会在控制台看到以下日志：
- `[Settings] Current accessStore state: {...}`
- `[Settings] Saving user config with accessStore data: {...}`
- `[Auth] Loading user config with accessStore data: {...}`

## 预期结果
- 自定义模型配置能正确保存到JSON文件
- 用户重新登录后配置能正确恢复
- 所有提供商的自定义URL都能正常保存和加载

## 注意事项
- 修复后需要重新保存一次设置才能生效
- 旧的配置文件可能需要手动更新或重新保存
- 建议用户在修复后重新设置一次自定义配置
